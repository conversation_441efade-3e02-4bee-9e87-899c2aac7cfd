<template>
  <aside class="sidebar left-sidebar" :class="{ collapsed: isCollapsed }">
    <!-- 悬浮展开收起按钮 -->
    <div class="collapse-toggle" @click="toggleCollapse" :title="isCollapsed ? '展开历史对话' : '收起历史对话'">
      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
        <path
          :d="isCollapsed ? 'M9 6l6 6-6 6' : 'M15 6l-6 6 6 6'"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>

    <div v-show="!isCollapsed" class="sidebar-content">
      <button class="new-chat-btn" @click="startNewChat">
        <img src="../../chat-v3/icons/plus.svg" alt="Plus" width="14" height="16" />
        <span style="margin-left: 10px;">开启新的对话</span>
      </button>

      <div class="history-section">
        <h3>
          历史对话
          <img
            v-if="list.length > 0"
            @click="handleDel()"
            src="../../chat-v3/icons/delete-all.svg"
            alt="Delete All"
            width="12"
            height="14"
            class="delete-all-icon"
            title="删除全部历史对话"
          />
        </h3>

        <div class="history-list" v-loading="loading" ref="scrollContainer">
          <div v-if="list.length === 0 && !loading" class="empty-history">
            <span>暂无历史对话</span>
          </div>
          <ul v-loading="loading" class="ulScrollbar">
              <div
                v-for="(item, index) in list"
                :key="item.id + getRandomNum()"
                class="history-item"
                :class="{ active: index === currentIndex }"
                @click="debounceHandle(item, index)"
              >
                <span class="history-title">{{ item.query }}</span>
                <div
                  class="menu-container"
                  @mouseenter="handleMouseEnter(item.id)"
                  @mouseleave="handleMouseLeave"
                >
                  <button
                    class="menu-btn"
                    title="更多操作"
                  >
                    <img src="../../chat-v3/icons/menu-dots.svg" alt="Menu" width="16" height="4" />
                  </button>

                  <!-- 下拉菜单 -->
                  <div
                    v-if="showMenuId === item.id"
                    class="dropdown-menu"
                    @click.stop
                  >
                    <div class="menu-item" @click="handleAdd(item)">
                      <img src="../../chat-v3/icons/star.svg" alt="Star" width="16" height="16" />
                      设置为常用
                    </div>
                    <div class="menu-item" @click="handleDel(item.id)">
                      <img src="../../chat-v3/icons/delete-all.svg" alt="Delete" width="12" height="14" />
                      删除
                    </div>
                  </div>
                </div>
              </div>
               <template v-if="list.length">
                <li v-if="isMore && !noMoreData" class="more">
                  <span>加载中...</span>
                  <PulseLoader :loading="true" size="8px" />
                </li>
                <li v-if="noMoreData" class="noMoreData">
                  <span>没有更多数据了</span>
                </li>
              </template>
          </ul>
        </div>
      </div>
       <SetHelp ref="SetHelp" @updataHelpList="updataHelpList" />
    </div>

    <!-- 收起状态下的占位内容 -->
    <div v-show="isCollapsed" class="collapsed-content">
      <div class="collapsed-title">历史对话</div>
    </div>
  </aside>
</template>

<script>
import throttle from "lodash/throttle";
import { getHistory } from "./commonReq.js";
import { listHelp, getHelpRank } from "@/api/techdocmanage/help";
import serviceKnowledge from "@/api/knowledge.js";
import { getUser } from "@/api/system/user";
import { parseMarkdown, isHtml } from "../utils";
import SetHelp from "./SetHelp.vue";
import { addHelp } from "@/api/techdocmanage/help";
import _debounce from "lodash/debounce";
import PulseLoader from "vue-spinner/src/PulseLoader.vue";
import { bus } from "@/bus";
export default {
  name: "LeftOptionList",
  components: { SetHelp, PulseLoader },
  props: {},
  data() {
    return {
      isCollapsed: false,
      options: [
        {
          label: "对话列表",
          value: 0,
        },
        //         {
        //   label: "功能中心",
        //   value: 1,
        // },
        {
          label: "常用问题",
          value: 2,
        },
      ],
      // 标题选中
      topCureent: 0,
      // 知识库名
      knowledgeName: null,
      // 问答历史列表
      list: [],
      currentIndex: null,
      hoveredIndex: null, // 鼠标悬停项的索引
      isHovered: false, // 是否有元素被鼠标悬停
      // 常见问题
      faq: null,
      // 是否常见问题
      isfaq: false,
      // 聊天配置
      chatSet: null,
      // 当前AI配置角色数组
      currentRoleArr: [],
      // 角色选中值
      currentRole: null,
      // 是否功能中心
      isOptionsCenter: false,
      loading: false,
      // 是否调试
      debugMode: "",
      // 匹配向量数
      top_k: 0,
      // 是否显示全部删除操作
      isShowAllDel: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      //是否加载更多
      isMore: false,
      noMoreData: false,
      infiniteDisabled: false,
      total: 0,
      showMenuId: null, // 控制哪个item的菜单显示
      hideTimer: null,
    };
  },
  computed: {},
  watch: {
  },
  created() {
    this.getConfigKey("debugMode").then((response) => {
      this.debugMode = response.msg;
    });
  },
  mounted() {},
  beforeDestroy() {
    // 移除滚动事件监听
    this.$refs.scrollContainer.removeEventListener("scroll", this.handleScroll);
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
    }
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    handleMouseEnter(id) {
      if (this.hideTimer) {
        clearTimeout(this.hideTimer);
        this.hideTimer = null;
      }
      this.showMenuId = id;
    },
    handleMouseLeave() {
      this.hideTimer = setTimeout(() => {
        this.showMenuId = null;
      }, 100); // 100ms 延迟，给用户时间移动到菜单
    },
    startNewChat() {
      this.currentIndex = null;
      this.$emit("new-chat");
    },
    initData(val) {
      this.$nextTick(() => {
        this.chatSet = val;
        this.top_k = val.top_k;
        this.knowledgeName = val.knowledge_name;
        this.currentIndex = null;
        this.isMore = false;
        this.noMoreData = false;
        this.queryParams.pageNum = 1;
        this.getList();
        this.getUserAIRoleAndModel();
        this.$forceUpdate();
        this.$refs.scrollContainer.scrollTop = 0;
      });
    },
    // 获取问答列表
    async getList() {
      this.loading = true;
      // const { code, data } = await getHistory(this.knowledgeName);
      const { code, rows, total } = await serviceKnowledge.getHistoryList({
        knowledge_name: this.knowledgeName,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        generate: "0",
      });
      if (code === 200) {
        // this.list = data;
        this.list = rows;
        this.total = total;
        this.loading = false;
        // this.infiniteDisabled = false
        // this.isMore = true;
        // 监听滚动事件并加上节流（500ms 内只能执行一次）
        this.$refs.scrollContainer.addEventListener(
          "scroll",
          throttle(this.handleScroll, 500) // 每 500ms 触发一次
        );
      }
    },
    // 生成随机数
    getRandomNum() {
      return Math.floor(Math.random() * 1000000);
    },
    // 监听滚动事件
    handleScroll() {
      const container = this.$refs.scrollContainer;
      const scrollTop = container.scrollTop;
      const clientHeight = container.clientHeight;
      const scrollHeight = container.scrollHeight;
      console.log(scrollTop + clientHeight >= scrollHeight);
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight) {
        this.isMore = true;
        this.loadMore();
      }
    },
    // 加载更多数据
    async loadMore() {
      if (this.noMoreData) return;

      // 模拟获取新数据
      const { code, rows, total } = await serviceKnowledge.getHistoryList({
        knowledge_name: this.knowledgeName,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        generate: "0",
      });
      if (code === 200) {
        if (this.list.length * 1 >= total) {
          this.isMore = false;
          this.noMoreData = true;
          console.log("没有更多数据", this.isMore);
          return;
        } else {
          // 非常用问题状态下更新列表数据
          if (!this.isfaq) {
                      this.list = [...this.list, ...rows];
              this.isMore = false;
              this.queryParams.pageNum++;
              console.log("触底加载", this.queryParams.pageNum);
          }else{
             this.isMore = false;
          }

        }
      }
    },
    // 获取常见问题列表
    async getListHelp() {
      this.loading = true;
      const { code, rows } = await listHelp({
        pageNum: 1,
        pageSize: 15,
        knowledgeName: this.knowledgeName,
        faq: this.faq,
      });
      if (code === 200) {
        this.list = rows.map((v) => {
          return {
            value: v.id,
            id: v.id,
            query: v.faq,
          };
        });
        this.loading = false;
      }
    },
    // 获取功能中心列表
    async getOptionsList() {
      this.loading = true;
      const { code, data } = await serviceKnowledge.getKnowledgeSetting();
      if (code === 200) {
        this.list = data
          .filter((v) => v.type === "1")
          .map((v, index) => {
            return {
              label: v.knowledge_name,
              query: v.knowledge_name,
              value: index,
              remark: v.remark,
              ...v,
            };
          });
        this.loading = false;
      }
    },
    // 常见问题列表设置
    getSetListHelp() {
      this.$refs.SetHelp.open(this.knowledgeName);
    },
    // 更新常见问题列表数据
    updataHelpList() {
      this.getListHelp();
    },
    debounceHandle(item, index) {
      this.currentIndex = index;
      this._debounceHandleClick(item, index);
    },
    _debounceHandleClick: _debounce(async function (item, index) {
      try {
        console.log(item);
              const { code, data } = await serviceKnowledge.getHistoryChat(item.id);
              if (code===200) {
                await this.handleClick(Object.assign(item,data), index);
              }

      } catch (error) {
        console.error("handleClick failed:", error);
      }
    }, 500),
    // 列表问题事件
    async handleClick(item, index) {
      if (this.isfaq) {
        await getHelpRank({ id: item.id });
        this.$emit("getUseFaq", item.query);
      } else {
        if (this.isOptionsCenter) {
          this.$emit("getOptions", { query: item.remark, ...item });
        } else {
          // let str = item.format_result ? item.format_result : item.result;
          console.log(item.result);
          let str = "";
          console.log(isHtml(item.result));
          if (isHtml(item.result)) {
            str = item.result;
          } else {
            str = item.format_result ? item.result : parseMarkdown(item.result);
          }

          this.$emit("getHistory", {
            id: item.id,
            answer: str,
            done: true,
            isStop: true,
            query: item.query,
            good: item.chat_like === "1" ? true : false,
            tableStr: item.format_result,
          });
          console.log("问答历史", {
            query: item.query,
            answer: str,
            good: item.chat_like === "1" ? true : false,
            bad: item.bad,
            id: item.id,
            tableStr: item.format_result,
          });
        }
      }
    },
    handleMouseOver(index) {
      this.hoveredIndex = index;
      this.isHovered = true;
    },
    handleMouseOut(index) {
      if (this.hoveredIndex === index) {
        this.hoveredIndex = null;
        this.isHovered = false;
      }
    },
    // 数据删除
    async handleDel(id) {
      const ids = id || this.list.map((v) => v.id);
      const { code, data } = await serviceKnowledge.getHistoryDeleted(ids);
      if (code === 200) {
        this.getList();
      }
    },
    // 添加到常用问题
    async handleAdd(item) {
      try {
        const res = await serviceKnowledge.getHistoryChat(item.id);
        if (res.code === 200 && res.data) {
          const { query, result } = res.data;
          const addResponse = await addHelp({
            knowledgeName: this.knowledgeName,
            faq: query,
            result: result,
          });
          if (addResponse.code === 200) {
            this.$message.success("常用问题新增成功");
            bus.$emit("refresh-common-questions");
          } else {
            this.$message.error(addResponse.msg || "新增失败");
          }
        } else {
          this.$message.error(res.msg || "获取对话详情失败");
        }
      } catch (error) {
        console.error("添加到常用问题失败:", error);
        this.$message.error("操作失败");
      }
    },
    handleRole() {
      this.$emit("getRole", { query: this.currentRole });
    },
    changeTopK() {
      this.$emit("updataTopK", { top_k: this.top_k });
    },
    async getUserAIRoleAndModel() {
      // 模型列表
      //       const resModel= await serviceKnowledge.getModelList({
      //   pageNum: 1,
      //   pageSize: 9999,
      // });
      // 角色列表
      const { code, rows } = await serviceKnowledge.getPromptList({
        pageNum: 1,
        pageSize: 9999,
        promptType: "knowledge_base_chat",
      });
      if (code === 200) {
        console.log(
          rows.find((i) => i.promptName === this.chatSet?.prompt_name)
        );
        this.currentRole = rows.find(
          (i) => i.promptName === this.chatSet?.prompt_name
        ).promptDescription;

        // 获取登录人配置角色id
        // const res = await getUser(this.$store.state.user.userId);
        // if (res.code === 200) {
        //   this.currentRoleArr = res.promptIds
        //     .map((v) => {
        //       const matchingRow = rows.find((i) => i.id === v);
        //       if (matchingRow) {
        //         return {
        //           id: v,
        //           label: matchingRow.promptDescription,
        //           value: matchingRow.promptName,
        //         };
        //       }
        //     })
        //     .filter((obj) => obj !== null); // 使用 filter 去除 null 值（如果有的话）

        //   this.currentRole =
        //     this.currentRoleArr.find(
        //       (v) => v.value === this.chatSet?.prompt_name
        //     )?.value || this.currentRoleArr[0]?.value;
        // }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sidebar {
  background-color: #ffffff;
  padding: 20px;
  height: 100%;
  overflow: hidden;
  position: relative;
  transition: width 0.3s ease, padding 0.3s ease;

  &.collapsed {
    width: 60px;
    min-width: 60px;
    padding: 20px 10px;
  }
}

.left-sidebar {
  width: 360px;
  min-width: 360px;
  flex-shrink: 0;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;

  &.collapsed {
    width: 60px;
    min-width: 60px;
  }
}

.collapse-toggle {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
  z-index: 1000;
  color: #666;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    background-color: rgba(245, 246, 247, 0.95);
    color: #333;
    transform: translateY(-50%) scale(1.05);
  }

  svg {
    flex-shrink: 0;
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow: hidden;
}

.collapsed-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .collapsed-title {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    font-size: 14px;
    color: #c6c8cc;
    letter-spacing: 2px;
  }
}

.new-chat-btn {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #e6f3fe;
  border: 1px solid #51a6f2;
  border-radius: 4px;
  color: #2d8cf0;
  font-size: 16px;
  cursor: pointer;
}

.new-chat-btn svg {
  margin-right: 8px;
}

.history-section h3 {
  font-size: 14px;
  color: #c6c8cc;
  margin: 0 0 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.delete-all-icon {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);

    path {
      fill: #f56c6c;
    }
  }
}

.history-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0; // 确保flex子元素可以收缩
  padding-bottom: 10px; // 防止底部被遮挡
  // HistoryItem 组件的样式在其自身文件中定义

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.empty-history {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.load-more {
  padding: 10px;
  text-align: center;

  button {
    background: none;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px 16px;
    color: #666;
    cursor: pointer;
    font-size: 12px;

    &:hover:not(:disabled) {
      border-color: #2d8cf0;
      color: #2d8cf0;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}

.ulScrollbar {
    padding-left: 0px !important;
    margin: 10px 0 10px 0px !important;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background-color: #ffffff;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #313233;
  position: relative;

  &:hover {
    background-color: #f5f5f5;

    .menu-btn {
      opacity: 1;
    }
  }

  &.active {
    background-color: #e6f3fe;
    color: #2d8cf0;
  }
}

.history-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  line-height: 1.4;
}

.menu-container {
  position: relative;
  flex-shrink: 0;
  margin-left: 8px;
}

.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
  margin-top: 4px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  font-size: 14px;
  color: #313233;
  cursor: pointer;
  transition: background-color 0.2s ease;

  img {
    margin-right: 8px;
    flex-shrink: 0;
  }

  svg {
    margin-right: 8px;
    flex-shrink: 0;
  }

  &:hover {
    background-color: #f5f5f5;
  }

  &:first-child {
    border-radius: 6px 6px 0 0;
  }

  &:last-child {
    border-radius: 0 0 6px 6px;
  }
}
</style>
