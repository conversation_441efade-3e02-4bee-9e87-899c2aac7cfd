<template>
  <div class="input-area">
    <div class="chat-footer-bar">
      <div class="chat-left">
        <textarea
          v-model.trim="prompt"
          placeholder="请输入您的问题"
          rows="1"
          @keydown.enter.prevent="handleSend"
        >
        </textarea>
      </div>
      <div class="chat-right action-buttons">
        <div class="voice-input-wrapper" :class="{ disabled: !canUseVoice }">
          <voice-input-button
              @record="handleVoiceResult"
              @record-start="handleVoiceStart"
              @record-stop="handleVoiceStop"
              @record-blank="handleVoiceBlank"
              @record-failed="handleVoiceFailed"
              @record-complete="handleVoiceComplete"
              interactiveMode="touch"
              color="transparent"
              tipPosition="top"
              class="custom-voice-btn"
          />
          <button
              class="mic-btn"
              :class="{ recording: isVoiceRecording, disabled: !canUseVoice }"
              :disabled="!canUseVoice"
              :title="voiceButtonTitle"
          >
            <img src="@/components/chat-v3/icons/microphone.svg" alt="Microphone" width="14" height="18"/>
          </button>
        </div>
        <button
            v-if="isStreaming"
            class="stop-btn"
            @click="handleStop"
            title="停止生成"
        >
          <img src="@/components/chat-v3/icons/stop.svg" alt="Stop" width="16" height="16"/>
        </button>

        <button
            v-else
            class="send-btn"
            @click="handleSend"
            :title="canSend ? '发送消息' : '请输入内容'"
            :disabled="!canSend"
        >
          <img src="@/components/chat-v3/icons/send.svg" alt="Send" width="20" height="19"/>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import VoiceInputButton from 'voice-input-button2';

export default {
  name: "InputChat",
  components: { VoiceInputButton },
  data() {
    return {
      prompt: "",
      isVoiceRecording: false,
    };
  },
  computed: {
    ...mapState('new-chat', ['chatSettings']),
    ...mapGetters('new-chat', ['isStreaming']),
    canSend() {
      return !this.isStreaming && !this.isVoiceRecording && this.prompt.trim().length > 0;
    },
    canUseVoice() {
      return !this.isStreaming;
    },
    voiceButtonTitle() {
      if (this.isStreaming) {
        return 'AI正在回答中，请稍候';
      }
      if (this.isVoiceRecording) {
        return '正在录音...点击停止';
      }
      return '点击开始语音输入';
    }
  },
  methods: {
    ...mapActions('new-chat', ['sendMessage', 'stopStreamChat']),
    async handleSend() {
      if (this.canSend) {
        await this.sendMessage({ text: this.prompt, chatSettings: this.chatSettings });
        this.prompt = "";
        this.$emit('message-sent');
      }
    },
    handleStop() {
      this.stopStreamChat();
    },
    handleEnter(event) {
      if (event.keyCode === 13 && !event.shiftKey) {
        this.handleSend();
        event.preventDefault();
      }
    },
    handleVoiceStart() {
      this.isVoiceRecording = true;
    },
    handleVoiceResult(text) {
      this.prompt = text;
    },
    handleVoiceStop() {
      this.isVoiceRecording = false;
    },
    handleVoiceBlank() {
      this.$message.warning('没有录到内容，请重试');
      this.isVoiceRecording = false;
    },
    handleVoiceComplete(text) {
      this.prompt = text;
      this.isVoiceRecording = false;
      this.handleSend();
    },
    handleVoiceFailed() {
      this.$message.error('语音识别失败，请重试');
      this.isVoiceRecording = false;
    }
  },
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/chatCommon.scss";

.input-area {
  background: #fff;
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  flex-shrink: 0;
  margin-top: auto;
  position: relative;
  z-index: 10;

  textarea {
    width: 100%;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    height: 80px;
    background-color: transparent;
    margin-bottom: 10px;
  }
}

.chat-footer-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 10px;
}
.chat-left {
  flex: 1;
  min-width: 0;
}
.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.send-btn,
.stop-btn,
.mic-btn {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-btn {
  background: #145bff;

  &:hover:not(:disabled) {
    background: #0d47d9;
  }

  &:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    opacity: 0.6;

    img {
      opacity: 0.5;
    }
  }
}

.stop-btn {
  background: #ff4757;

  &:hover {
    background: #ff3838;
  }
}

.voice-input-wrapper {
  position: relative;
  display: inline-block;

  &.disabled {
    pointer-events: none;
    opacity: 0.6;
  }
}

.mic-btn {
  background: #6b7280;
  position: relative;
  z-index: 1;

  img {
    filter: brightness(0) invert(1);
  }

  &:hover:not(.disabled) {
    background: #4b5563;
  }

  &.recording {
    background: #ef4444;
    animation: pulse 1.5s infinite;
  }

  &.disabled {
    background: #d1d5db;
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      background: #d1d5db;
    }
  }
}

.custom-voice-btn {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 40px !important;
  height: 40px !important;
  opacity: 0 !important;
  z-index: 10 !important;
  border-radius: 50% !important;
  cursor: pointer !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
</style>
