<template>
  <div class="container">
    <z-dialog
      :title="chatDialog.titleChat"
      :show.sync="chatDialog.openChat"
      ref="zdialog"
      v-if="chatDialog.openChat"
      @closeOnClickModal="closeOnClickModal"
      @handleMinimize="handleMinimize"
      :isFooter="false"
    >
      <div slot="body" class="content">
        <!-- 知识库选择 -->
        <div class="contentMain">
          <!-- 左侧对话列表 历史记录  功能 -->
          <LeftOptionList
            ref="LeftOptionList"
            @getUseFaq="getUseFaq"
            @getOptions="getOptions"
            @getHistory="getHistory"
            @updataTopK="updataTopK"
            @new-chat="handleNewChat"
          />
          <!-- 对话框主体 -->
          <ChatMain
            ref="ChatMain"
            @updataAskList="updataAskList"
            @handleMinimize="
              () => {
                this.$refs.zdialog.minimize();
              }
            "
            @getknowledgeValue="getknowledgeValue"
          />
		  <RightOptionList></RightOptionList>
        </div>
      </div>
      <div slot="footer">对话框footer</div>
    </z-dialog>
  </div>
</template>

<script>
import ZDialog from "@/components/ZDialog";
import ChatMain from "./components/ChatMain.vue";
import LeftOptionList from "./components/LeftOptionList.vue";
import RightOptionList from "./components/RightOptionList.vue";
import { getChatSetting } from "./components/commonReq.js";
import knowledgeApi from "@/api/knowledge.js";

export default {
  name: "",
  components: { ZDialog, LeftOptionList, ChatMain, RightOptionList },
  props: {
    oldChat:{
      type:String
    }
  },
  data() {
    return {
      // 聊天对话框配置
      chatDialog: {
        titleChat: "知识库AI助手",
        openChat: false,
        isShrink: true,
        chatParams: {
          knowledge_base_name: "",
          knowledgeList: [],
        },
      },
      flag: "",
      knowledge_name: "",
      setKnowledge: "",
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    open() {
      this.chatDialog.openChat = true;
      // 等待弹窗完全打开后设置为全屏
      this.$nextTick(() => {
        if (this.$refs.zdialog) {
          // this.$refs.zdialog.setFullscreen();
        }
      });
    },
    //关闭最小化弹框
    closeMinimize() {
      this.$refs.zdialog.closeDialog();
      setTimeout(() => {
        this.chatDialog.openChat = true;
        // 等待弹窗完全打开后设置为全屏
        this.$nextTick(() => {
          if (this.$refs.zdialog) {
            this.$refs.zdialog.setFullscreen();
          }
        });
      }, 200);
    },
    handleMinimize() {
      this.$emit("handleMinimize");
      // if (this.$store.state.common.isEnterpriseKnowledge) {
      //         this.$refs.KnowledgeSelect&&this.$refs.KnowledgeSelect.getList();
      // }
      // console.log(this.$store.state.common.isEnterpriseKnowledge);
      // 确保对话框状态正确关闭
      this.chatDialog.openChat = false;

      if (this.$store.state.common.isEnterpriseKnowledge) {
        this.$refs.KnowledgeSelect && this.$refs.KnowledgeSelect.getList();
      }

    },
    closeOnClickModal() {
      this.chatDialog.openChat = false;
      this.$emit("closeOnClickModal");
      this.$store.commit("common/isEnterpriseKnowledge", false);
    },
    // 获取选中知识库
    async getknowledgeValue(knowledge) {
      const isFileChat = this.$store.state.common.files.length > 0 && this.oldChat;
      let knowledge_name;

      if (isFileChat) {
        knowledge_name = this.oldChat;
      } else if (knowledge && knowledge.label) {
        knowledge_name = knowledge.label;
      } else {
        return; // 如果无法确定知识库，则不执行任何操作
      }

      // 获取聊天初始化配置
      const { code, data } = await getChatSetting(knowledge_name);
      if (code === 200 && data && data.length > 0) {
        this.knowledge_name = knowledge_name;
        this.setKnowledge = data;
        const settings = { ...data[0], knowledge_name };
        this.$store.commit("new-chat/SET_CHAT_SETTINGS", settings);

        let knowledge_for_store;
        if (isFileChat) {
          // 在文件对话模式下，我们创建一个临时的 knowledge 对象，并且不尝试生成图片 URL
          knowledge_for_store = {
            label: knowledge_name,
            img: settings.image || "",
            image: "", // 避免调用不可靠的 ref
          };
        } else {
          // 在普通模式下，knowledge 对象是有效的，可以安全地生成图片 URL
          knowledge_for_store = {
            ...knowledge,
            image: this.$refs.ChatMain.$refs.KnowledgeSelect.getImageUrl(
              knowledge.img
            ),
          };
        }

        this.$store.commit("chat-v3/SET_CURRENT_KNOWLEDGE", knowledge_for_store);
        this.$refs.ChatMain.clearMessages(); // 切换知识库时清空消息
        this.$refs.LeftOptionList.initData(settings);
        this.$refs.ChatMain.initData(settings);
      }
    },
    // 常见问题回调
    getUseFaq(val) {
      this.$refs.ChatMain.askQuestion(val, "0");
    },
    // 功能中心回调
    getOptions(val) {
      this.$refs.ChatMain.askQuestion(val, "1");
    },
    // 对话列表回调
    async getHistory(historyItem) {
      try {
        await this.$store.dispatch('new-chat/selectChat', historyItem.id);
      } catch (error) {
        console.error('获取历史记录详情失败:', error);
        this.$message.error('获取历史记录详情失败');
      }
    },
    handleNewChat() {
      this.$refs.ChatMain.clearMessages();
    },
    // 更新左侧对话列表
    updataAskList() {
      this.$refs.LeftOptionList.getList();

    },
    updataTopK(val) {
      const { top_k } = val;
      let params = {
        ...this.setKnowledge[0],
        top_k,
        knowledge_name: this.knowledge_name,
      };
      this.$refs.LeftOptionList.initData(params);
      this.$refs.ChatMain.initData(params);
    },
  },
};
</script>

<style scoped lang="scss">
.content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .contentMain {
    flex: 1;
    min-height: 0; // 确保flex子元素可以收缩
    width: 100%;
    display: flex;
    overflow: hidden;
  }
}
</style>
