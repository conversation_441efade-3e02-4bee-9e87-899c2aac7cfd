import { getToken } from '@/utils/auth'
import knowledgeApi from '@/api/knowledge.js'
import { bus } from '@/bus'

// 获取聊天配置参数
function getChatConfig(settings) {
  return {
    model_name: settings.model_name,
    prompt_name: settings.prompt_name,
    score_threshold: settings.score_threshold,
    top_k: settings.top_k,
    temperature: settings.temperature,
    mix_type: settings.mix_type,
    max_tokens: settings.max_tokens
  }
}

// 格式化历史记录项
function formatHistoryItem(item) {
  if (!item) return null;
  return {
    ...item,
    liked: item.chat_like == 1,
    timestamp: new Date(item.create_time).getTime(),
    done: true
  };
}

const state = {
  messages: [],
  isSending: false,
  streaming: {
    active: false,
    messageId: null,
    answer: ''
  },
  abortController: null,
  chatSettings: {}
}

const getters = {
  canSendMessage: state => !state.isSending && !state.streaming.active,
  isStreaming: state => state.streaming.active,
  sortedMessages: state => [...state.messages].sort((a, b) => a.timestamp - b.timestamp)
}

const mutations = {
  ADD_MESSAGE(state, message) {
    const newMessage = {
      id: message.id || Date.now() + Math.random(),
      timestamp: message.timestamp || Date.now(),
      docs: [], // 确保 docs 字段被 Vue 响应式系统正确跟踪
      ...message
    }
    state.messages.push(newMessage)
  },
  UPDATE_MESSAGE(state, { id, updates }) {
    const index = state.messages.findIndex(msg => msg.id === id)
    if (index !== -1) {
      // 保护 docs 字段不被意外覆盖
      if (!updates.hasOwnProperty('docs') && state.messages[index].docs) {
        updates = { ...updates, docs: state.messages[index].docs }
      }
      Object.assign(state.messages[index], updates)
    }
  },
  SET_SENDING(state, status) {
    state.isSending = status
  },
  SET_STREAMING(state, streaming) {
    state.streaming = { ...state.streaming, ...streaming }
  },
  RESET_STREAMING(state) {
    state.streaming = { active: false, messageId: null, answer: '' }
  },
  SET_CHAT_SETTINGS(state, settings) {
    state.chatSettings = settings || {}
  },
  CLEAR_MESSAGES(state) {
    state.messages = []
  },
  SET_MESSAGES(state, messages) {
    state.messages = messages
  },
  DELETE_MESSAGE(state, messageId) {
    state.messages = state.messages.filter(msg => msg.id !== messageId)
  }
}

const actions = {
  sendMessage({ commit, state }, { text, chatSettings }) {
    return new Promise((resolve, reject) => {
      try {
        commit('SET_SENDING', true)

        commit('ADD_MESSAGE', {
          type: 'user',
          answer: text.trim(),
        })

        const aiMessage = {
          type: 'assistant',
          answer: '',
          isStreaming: true,
          done: false
        }
        commit('ADD_MESSAGE', aiMessage)
        const newAiMessageId = state.messages[state.messages.length - 1].id

        commit('SET_STREAMING', {
          active: true,
          messageId: newAiMessageId,
          answer: ''
        })

        // 格式化历史记录
        const formattedHistory = state.messages
          .filter(msg => !msg.loading && !msg.error && msg.answer)
          .slice(-10) // 限制最近10条
          .map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.answer
          }))
          .filter(msg => msg.content.trim() !== '');

        // 根据知识库配置决定是否启用历史对话
        const shouldUseHistory = chatSettings.history === "1" || chatSettings.history === 1;
        const historyData = shouldUseHistory ? formattedHistory : [];

        const requestBody = {
          query: text,
          knowledge_base_name: chatSettings.knowledge_name,
          stream: true,
          history: historyData, // 使用处理后的历史记录
          model_name: chatSettings.model_name,
          temperature: chatSettings.temperature,
          max_tokens: 0,
          prompt_name: chatSettings.prompt_name,
        }

        state.abortController = new AbortController()

        fetch('/stream-api/chat/knowledge_base_chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getToken()
          },
          body: JSON.stringify(requestBody),
          signal: state.abortController.signal
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`请求失败: ${response.status}`)
            }

            const reader = response.body.getReader()
            const decoder = new TextDecoder()
            let buffer = ''

            function readStream() {
              return reader.read().then(({ done, value }) => {
                if (done) {
                  commit('SET_STREAMING', { active: false, messageId: null, answer: '' });
                  const finalAiMessage = state.messages.find(m => m.id === newAiMessageId);
                  if (finalAiMessage) {
                    commit('UPDATE_MESSAGE', {
                      id: newAiMessageId,
                      updates: { isStreaming: false, done: true }
                    });

                    const userMessage = state.messages.find(m => m.type === 'user' && m.answer === text.trim());
                    if (userMessage && finalAiMessage.answer) {
                      const saveConfig = getChatConfig(chatSettings);
                      knowledgeApi.saveDatabase({
                        knowledge_name: chatSettings.knowledge_name,
                        query: userMessage.answer,
                        result: finalAiMessage.answer,
                        type: chatSettings?.label || "",
                        generate: 0,
                        ...getChatConfig(chatSettings)
                      }).then(response => {
                        if (response.code === 200) {
                          commit('UPDATE_MESSAGE', {
                            id: newAiMessageId,
                           updates: { historyId: response.msg }
                         });
                         bus.$emit('refresh-chat-history')
                       }
                     }).catch(error => {
                       console.error('保存历史记录失败:', error);
                     });
                    }
                  }
                  commit('SET_SENDING', false);
                  resolve();
                  return;
                }

                const chunk = decoder.decode(value, { stream: true })
                buffer += chunk

                const lines = buffer.split('\n')
                buffer = lines.pop() || ''

                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    try {
                      const jsonStr = line.slice(6).trim()

                      if (jsonStr && jsonStr !== '[DONE]') {
                        const data = JSON.parse(jsonStr)
                        const updateData = {}

                        // 处理 answer 字段
                        if (data.answer) {
                          const currentAnswer = state.streaming.answer + data.answer
                          commit('SET_STREAMING', {
                            active: true,
                            messageId: newAiMessageId,
                            answer: currentAnswer
                          })
                          updateData.answer = currentAnswer
                        }

                        // 处理 docs 字段 - 独立于 answer
                        if (data.docs && data.docs.length > 0) {
                          updateData.docs = data.docs
                        }

                        // 只有当有数据需要更新时才调用 UPDATE_MESSAGE
                        if (Object.keys(updateData).length > 0) {
                          commit('UPDATE_MESSAGE', {
                            id: newAiMessageId,
                            updates: updateData
                          })
                        }
                      }
                    } catch (e) {
                      console.warn('解析流数据失败:', e, '原始数据:', line)
                    }
                  }
                }

                return readStream()
              })
            }

            return readStream()
          })
          .catch(error => {
            if (error.name !== 'AbortError') {
              console.error('流式请求错误:', error)
              commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
              commit('SET_SENDING', false)
              commit('UPDATE_MESSAGE', {
                id: newAiMessageId,
                updates: {
                  answer: '抱歉，回答生成失败，请重试',
                  isStreaming: false,
                  done: true,
                  error: true
                }
              })
              reject(error)
            }
          })

      } catch (error) {
        commit('SET_SENDING', false)
        commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
        reject(error)
      }
    })
  },

  stopStreamChat({ commit, state }) {
    const messageId = state.streaming.messageId

    if (state.abortController) {
      state.abortController.abort()
      state.abortController = null
    }

    commit('SET_STREAMING', { active: false, messageId: null, answer: '' })
    commit('SET_SENDING', false)

    if (messageId) {
      commit('UPDATE_MESSAGE', {
        id: messageId,
        updates: { isStreaming: false, done: true }
      })
    }
  },

  async selectChat({ commit }, chatId) {
    const response = await knowledgeApi.getHistoryChat(chatId);
    if (response.code !== 200) {
      throw new Error(response.msg || '获取历史记录详情失败');
    }

    const chatDetail = formatHistoryItem(response.data);
    commit('CLEAR_MESSAGES');

    commit('ADD_MESSAGE', {
      type: 'user',
      answer: chatDetail.query,
      timestamp: chatDetail.timestamp
    });

    commit('ADD_MESSAGE', {
      type: 'assistant',
      answer: chatDetail.result,
      timestamp: chatDetail.timestamp,
      liked: chatDetail.liked,
      historyId: chatDetail.id,
      done: true
    });
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
