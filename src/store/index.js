import newChat from './modules/new-chat'
import Vue from "vue";
import Vuex from "vuex";
import app from "./modules/app";
import user from "./modules/user";
import tagsView from "./modules/tagsView";
import permission from "./modules/permission";
import settings from "./modules/settings";
import common from "./modules/common";
import host from "./modules/host";
import chatV3 from "./modules/chat-v3/index";
import state from "./state.js";
import getters from "./getters.js";
import mutations from "./mutations.js";
import actions from "./actions.js";

// 组态平台接入；
import login from "./modules/login";
import frame from "./modules/frame";
import tab from "./modules/tab";
import menu from "./modules/menu";
import element from "./modules/element";
import canvas from "./modules/canvas";

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    login,
    frame,
    tab,
    menu,
    app,
    user,
    tagsView,
    permission,
    settings,
    common,
    element,
    canvas,
    host,
    'chat-v3': chatV3,
    'new-chat': newChat,
  },
  getters,
  state,
  mutations,
  actions,
});

export default store;
